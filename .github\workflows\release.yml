name: Release Build

on: workflow_dispatch

env:
  VERSION: "0.1.57"
  MESSAGE: "🌙 V{0} released with Dark Titlebar support! Now matches your Windows theme automatically ✨🎉" # Using {0} as a simple placeholder

jobs:
  build-and-release:
    runs-on: windows-latest
    permissions:
      contents: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Build application
      run: python build.py

    - name: Get commit messages
      id: commits
      run: |
        # Get last release tag
        $last_tag = git describe --tags --abbrev=0
        # Get all commits since last release
        $commits = git log --pretty=format:'- %s' $last_tag..HEAD
        echo "changelog=$commits" | Out-File -FilePath $env:GITHUB_OUTPUT -Encoding utf8 -Append

    - name: Prepare Release Message
      id: release_message_prep
      run: |
        $rawMessage = "${{ env.MESSAGE }}"
        $version = "${{ env.VERSION }}"
        $finalMessage = $rawMessage.Replace("{0}", $version)
        echo "final_message=$finalMessage" | Out-File -FilePath $env:GITHUB_OUTPUT -Encoding utf8 -Append

    - name: Rename build.zip to DeepSeekChat-windows.zip
      run: Rename-Item -Path .\zipped\build.zip -NewName DeepSeekChat-windows.zip

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: v${{ env.VERSION }}
        name: DeepSeek Desktop V${{ env.VERSION }}
        body: |
          ${{ steps.release_message_prep.outputs.final_message }}

          **Changelog:**
          ${{ steps.commits.outputs.changelog }}
        draft: false
        prerelease: false
        files: zipped/DeepSeekChat-windows.zip