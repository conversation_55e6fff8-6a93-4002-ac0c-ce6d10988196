name: Release Build

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version'
        required: false
        default: ''
      message:
        description: 'Release message'
        required: false
        default: ''

env:
  VERSION: "0.1.58"
  MESSAGE: "🚀 V{0} released with enhanced performance, security improvements, and better error handling! ✨🎉"

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety

    - name: Run security scan
      run: |
        bandit -r . -f json -o bandit-report.json || true
        safety check --json --output safety-report.json || true

    - name: Upload security reports
      uses: actions/upload-artifact@v4
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  build-and-release:
    runs-on: windows-latest
    needs: security-scan
    permissions:
      contents: write
      security-events: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        cache: 'pip'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Validate build environment
      run: |
        python --version
        pip list
        python -c "import webview; print(f'pywebview version: {webview.__version__}')"
        python -c "import PyInstaller; print(f'PyInstaller version: {PyInstaller.__version__}')"

    - name: Build application
      run: python build.py

    - name: Verify build artifacts
      run: |
        if (!(Test-Path "built\DeepSeekChat.exe")) {
          Write-Error "Executable not found!"
          exit 1
        }
        if (!(Test-Path "zipped\build.zip")) {
          Write-Error "ZIP archive not found!"
          exit 1
        }
        Write-Host "Build artifacts verified successfully"

    - name: Get commit messages
      id: commits
      run: |
        try {
          $last_tag = git describe --tags --abbrev=0 2>$null
          if ($LASTEXITCODE -eq 0) {
            $commits = git log --pretty=format:'- %s' "$last_tag..HEAD"
          } else {
            $commits = git log --pretty=format:'- %s' --max-count=10
          }
          $commits = $commits -join "`n"
          echo "changelog=$commits" | Out-File -FilePath $env:GITHUB_OUTPUT -Encoding utf8 -Append
        } catch {
          echo "changelog=- Initial release" | Out-File -FilePath $env:GITHUB_OUTPUT -Encoding utf8 -Append
        }

    - name: Prepare Release Message
      id: release_message_prep
      run: |
        $version = if ("${{ github.event.inputs.version }}" -ne "") {
          "${{ github.event.inputs.version }}"
        } else {
          "${{ env.VERSION }}"
        }

        $message = if ("${{ github.event.inputs.message }}" -ne "") {
          "${{ github.event.inputs.message }}"
        } else {
          "${{ env.MESSAGE }}"
        }

        $finalMessage = $message.Replace("{0}", $version)
        echo "final_message=$finalMessage" | Out-File -FilePath $env:GITHUB_OUTPUT -Encoding utf8 -Append
        echo "version=$version" | Out-File -FilePath $env:GITHUB_OUTPUT -Encoding utf8 -Append

    - name: Generate checksums
      run: |
        $hash = Get-FileHash -Path "zipped\build.zip" -Algorithm SHA256
        $hash.Hash | Out-File -FilePath "zipped\build.zip.sha256" -Encoding utf8
        Write-Host "SHA256: $($hash.Hash)"

    - name: Rename build.zip to DeepSeekChat-windows.zip
      run: |
        Rename-Item -Path "zipped\build.zip" -NewName "DeepSeekChat-windows.zip"
        Rename-Item -Path "zipped\build.zip.sha256" -NewName "DeepSeekChat-windows.zip.sha256"

    - name: Create Release
      uses: softprops/action-gh-release@v2
      with:
        tag_name: v${{ steps.release_message_prep.outputs.version }}
        name: DeepSeek Desktop V${{ steps.release_message_prep.outputs.version }}
        body: |
          ${{ steps.release_message_prep.outputs.final_message }}

          ## 🔧 What's New
          ${{ steps.commits.outputs.changelog }}

          ## 📦 Installation
          1. Download `DeepSeekChat-windows.zip`
          2. Extract the ZIP file
          3. Run `DeepSeekChat.exe`
          4. Optionally run `auto-update.bat` for automatic updates

          ## 🔒 Security
          - SHA256 checksum is provided for verification
          - All dependencies are pinned to specific versions
          - Security scanning performed during build

          ## 🐛 Issues
          Report bugs at: https://github.com/LousyBook94/DeepSeek-Desktop/issues
        draft: false
        prerelease: false
        files: |
          zipped/DeepSeekChat-windows.zip
          zipped/DeepSeekChat-windows.zip.sha256
        generate_release_notes: true