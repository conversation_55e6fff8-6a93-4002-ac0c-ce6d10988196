import webview
import os
import argparse
import sys
import platform
import logging
import threading
import time
from pathlib import Path
from typing import Optional
from dataclasses import dataclass

APP_TITLE = "DeepSeek - Into the Unknown"
DEFAULT_URL = "https://chat.deepseek.com"

# Configuration class for better organization
@dataclass
class AppConfig:
    """Application configuration."""
    title: str = APP_TITLE
    url: str = DEFAULT_URL
    width: int = 1200
    height: int = 800
    storage_path: str = "./data"
    injection_path: str = "injection/inject.js"
    updater_path: str = "auto-update.bat"

    # Thread-safe properties
    _verbose_logs: bool = True
    _titlebar_preference: str = 'auto'
    _lock: threading.Lock = threading.field(default_factory=threading.Lock)

    @property
    def verbose_logs(self) -> bool:
        with self._lock:
            return self._verbose_logs

    @verbose_logs.setter
    def verbose_logs(self, value: bool) -> None:
        with self._lock:
            self._verbose_logs = value

    @property
    def titlebar_preference(self) -> str:
        with self._lock:
            return self._titlebar_preference

    @titlebar_preference.setter
    def titlebar_preference(self, value: str) -> None:
        with self._lock:
            self._titlebar_preference = value

# Global configuration instance
app_config = AppConfig()

def setup_logging(verbose: bool = True) -> logging.Logger:
    """Set up application logging."""
    level = logging.DEBUG if verbose else logging.WARNING
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('deepseek_desktop.log', mode='a')
        ]
    )
    return logging.getLogger(__name__)

def _log(msg: str, level: str = 'info') -> None:
    """Thread-safe logging function."""
    if app_config.verbose_logs:
        logger = logging.getLogger(__name__)
        getattr(logger, level.lower(), logger.info)(msg)

# Windows-specific imports for dark titlebar
if platform.system() == "Windows":
    try:
        import ctypes
        from ctypes import wintypes
        import winreg
    except ImportError:
        ctypes = None
        wintypes = None
        winreg = None

# Global variable to store titlebar preference
titlebar_preference = 'auto'

def is_dark_mode_enabled() -> bool:
    """Check if Windows is using dark mode.

    Returns:
        True if dark mode is enabled, False otherwise
    """
    if platform.system() != "Windows" or not winreg:
        return False

    try:
        # Check the registry for dark mode setting
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                           r"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize") as registry_key:
            value, _ = winreg.QueryValueEx(registry_key, "AppsUseLightTheme")
            # Value is 0 for dark mode, 1 for light mode
            return value == 0
    except (FileNotFoundError, OSError, Exception) as e:
        _log(f"Could not read dark mode setting: {e}", 'warning')
        # Default to light mode if we can't read the registry
        return False

def find_window_handle(window_title: str) -> Optional[int]:
    """Find window handle by title with retry logic.

    Args:
        window_title: The window title to search for

    Returns:
        Window handle if found, None otherwise
    """
    if platform.system() != "Windows" or not ctypes:
        return None

    try:
        user32 = ctypes.windll.user32

        # Try exact title match first
        hwnd = user32.FindWindowW(None, window_title)
        if hwnd:
            return hwnd

        # Try to enumerate all windows and find by partial title match
        # Define callback with proper WinAPI types
        EnumWindowsProc = ctypes.WINFUNCTYPE(wintypes.BOOL, wintypes.HWND, ctypes.py_object)
        def enum_windows_callback(hwnd, lst):
            try:
                length = user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    user32.GetWindowTextW(hwnd, buffer, length + 1)
                    if window_title.lower() in buffer.value.lower():
                        lst.append(hwnd)
                        return wintypes.BOOL(False)  # Stop enumeration
            except Exception:
                pass  # Skip problematic windows
            return wintypes.BOOL(True)  # Continue enumeration

        callback = EnumWindowsProc(enum_windows_callback)
        user32.EnumWindows.argtypes = [EnumWindowsProc, ctypes.py_object]
        user32.EnumWindows.restype = wintypes.BOOL

        # List to store found window handle
        found_windows = []
        user32.EnumWindows(callback, ctypes.py_object(found_windows))

        if found_windows:
            return found_windows[0]

        return None
    except Exception as e:
        _log(f"Error finding window handle: {e}", 'error')
        return None

def should_use_dark_titlebar() -> bool:
    """Determine if dark titlebar should be used based on preference and system settings.

    Returns:
        True if dark titlebar should be used, False otherwise
    """
    preference = app_config.titlebar_preference

    if preference == 'dark':
        return True
    elif preference == 'light':
        return False
    else:  # auto
        return is_dark_mode_enabled()

def apply_dark_titlebar(window) -> bool:
    """Apply dark titlebar to the window on Windows.

    Args:
        window: The webview window instance

    Returns:
        True if titlebar was applied successfully, False otherwise
    """
    if platform.system() != "Windows" or not ctypes:
        return True  # Return True on non-Windows platforms (no-op success)

    try:
        # Get the window handle
        hwnd = None

        # Try to get the window handle from different possible attributes
        for attr_path in ['hwnd', '_window.hwnd', 'gui.hwnd']:
            try:
                obj = window
                for attr in attr_path.split('.'):
                    obj = getattr(obj, attr)
                if obj:
                    hwnd = obj
                    break
            except AttributeError:
                continue

        # If we couldn't get it from the window object, try to find it by title
        if not hwnd:
            hwnd = find_window_handle(APP_TITLE)

        if hwnd:
            # Constants for DwmSetWindowAttribute
            DWMWA_USE_IMMERSIVE_DARK_MODE_NEW = 20  # Win10 1903+
            DWMWA_USE_IMMERSIVE_DARK_MODE_OLD = 19  # Win10 1809 and earlier

            # Load dwmapi.dll
            dwmapi = ctypes.windll.dwmapi

            # Define function signature: HRESULT DwmSetWindowAttribute(HWND, DWORD, LPCVOID, DWORD)
            try:
                dwmapi.DwmSetWindowAttribute.argtypes = [
                    ctypes.c_void_p,  # HWND
                    ctypes.c_int,     # DWORD attribute
                    ctypes.c_void_p,  # LPCVOID pvAttribute
                    ctypes.c_uint     # DWORD cbAttribute
                ]
                dwmapi.DwmSetWindowAttribute.restype = ctypes.c_int  # HRESULT
            except AttributeError:
                # Older systems may not expose the symbol; keep best-effort behavior
                _log("DwmSetWindowAttribute not available on this system", 'warning')
                return False

            # Determine if we should use dark mode
            use_dark = should_use_dark_titlebar()
            dark_mode = ctypes.c_int(1 if use_dark else 0)

            # Try new attribute value first
            result = dwmapi.DwmSetWindowAttribute(
                ctypes.c_void_p(hwnd),
                ctypes.c_int(DWMWA_USE_IMMERSIVE_DARK_MODE_NEW),
                ctypes.byref(dark_mode),
                ctypes.sizeof(dark_mode)
            )

            # Fallback to old attribute value on failure
            if result != 0:
                result = dwmapi.DwmSetWindowAttribute(
                    ctypes.c_void_p(hwnd),
                    ctypes.c_int(DWMWA_USE_IMMERSIVE_DARK_MODE_OLD),
                    ctypes.byref(dark_mode),
                    ctypes.sizeof(dark_mode)
                )

            if result == 0:  # S_OK
                mode_str = 'dark' if use_dark else 'light'
                preference = app_config.titlebar_preference
                source_str = f"({preference} mode)" if preference != 'auto' else "(system theme)"
                _log(f"Titlebar set to {mode_str} {source_str}")
                return True
            else:
                _log(f"Failed to set titlebar theme: error code {result}", 'warning')
                return False
        else:
            _log("Could not find window handle for titlebar theming", 'warning')
            return False

    except Exception as e:
        _log(f"Error applying titlebar theme: {e}", 'error')
        return False

def apply_dark_titlebar_delayed(window) -> None:
    """Apply dark titlebar with a delay to ensure window is fully created.

    Args:
        window: The webview window instance
    """
    def delayed_apply():
        # Wait a bit for the window to be fully created
        time.sleep(0.5)

        # Try multiple times with progressive backoff
        for attempt in range(5):
            if apply_dark_titlebar(window):
                return  # Success, stop trying
            time.sleep(0.5 * (attempt + 1))  # Progressive backoff

        _log("Failed to apply dark titlebar after multiple attempts", 'warning')

    # Run in a separate thread to avoid blocking
    thread = threading.Thread(target=delayed_apply, daemon=True)
    thread.start()

def inject_js(window) -> bool:
    """Inject JavaScript into the webview window.

    Args:
        window: The webview window instance

    Returns:
        True if injection succeeded, False otherwise
    """
    try:
        injection_path = Path(app_config.injection_path)
        if not injection_path.exists():
            _log(f"Injection file not found: {injection_path}", 'warning')
            return False

        # Read injection script with proper encoding
        js_code = injection_path.read_text(encoding='utf-8')

        # Inject JavaScript
        window.evaluate_js(js_code)
        _log("JavaScript injection successful")
        return True

    except Exception as e:
        _log(f"Error injecting JavaScript: {e}", 'error')
        return False

def launch_auto_updater() -> bool:
    """Launch the auto-updater in background mode.

    Returns:
        True if updater launched successfully, False otherwise
    """
    try:
        updater_path = Path(app_config.updater_path)
        if not updater_path.exists():
            _log(f"Auto-updater not found: {updater_path}", 'warning')
            return False

        # Launch updater in background with auto mode
        import subprocess
        subprocess.Popen(
            [str(updater_path), '--auto'],
            creationflags=subprocess.CREATE_NEW_CONSOLE,
            cwd=updater_path.parent
        )
        _log("Auto-updater launched successfully")
        return True

    except Exception as e:
        _log(f"Failed to launch auto-updater: {e}", 'error')
        return False


def on_window_loaded(window) -> None:
    """Called when window is loaded.

    Args:
        window: The webview window instance
    """
    # Apply dark titlebar with delay to ensure window is fully created
    apply_dark_titlebar_delayed(window)
    # Inject JavaScript
    inject_js(window)

def main() -> None:
    """Main application entry point."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="DeepSeek Desktop - Enhanced chat experience",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument('--release', action='store_true',
                       help='Disable debug tools for release build')
    parser.add_argument('--url', type=str, default=DEFAULT_URL,
                       help=f'Custom URL to load (default: {DEFAULT_URL})')
    parser.add_argument('--no-updater', action='store_true',
                       help='Disable auto-updater launch')

    titlebar_group = parser.add_mutually_exclusive_group()
    titlebar_group.add_argument('--dark-titlebar', action='store_true',
                               help='Force dark titlebar')
    titlebar_group.add_argument('--light-titlebar', action='store_true',
                               help='Force light titlebar')

    args = parser.parse_args()

    # Configure application
    if args.dark_titlebar:
        app_config.titlebar_preference = 'dark'
    elif args.light_titlebar:
        app_config.titlebar_preference = 'light'
    else:
        app_config.titlebar_preference = 'auto'

    # Auto-enable release mode for frozen builds
    is_frozen = getattr(sys, 'frozen', False)
    release_mode = args.release or is_frozen
    app_config.verbose_logs = not release_mode

    # Set up logging
    logger = setup_logging(app_config.verbose_logs)
    logger.info(f"Starting {APP_TITLE}")
    logger.info(f"Release mode: {release_mode}")
    logger.info(f"Titlebar preference: {app_config.titlebar_preference}")

    # Validate URL
    target_url = args.url
    if not target_url.startswith(('http://', 'https://')):
        logger.error(f"Invalid URL: {target_url}")
        sys.exit(1)

    # Launch auto-updater in background (unless disabled)
    if not args.no_updater:
        launch_auto_updater()

    # Ensure storage directory exists
    storage_path = Path(app_config.storage_path)
    storage_path.mkdir(exist_ok=True)

    try:
        # Create window with persistent cookie storage
        window = webview.create_window(
            app_config.title,
            target_url,
            width=app_config.width,
            height=app_config.height,
            text_select=True  # Enable selecting text (#2 vanja-san)
        )

        # Add event listener for page load
        window.events.loaded += on_window_loaded

        # Start webview with persistent storage
        webview.start(
            private_mode=False,  # Disable private mode for persistent cookies
            storage_path=str(storage_path),  # Storage directory
            debug=not release_mode  # Enable dev tools unless in release mode
        )

    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()