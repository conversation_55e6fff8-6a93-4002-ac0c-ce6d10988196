import os
import shutil
import subprocess
import sys
import zipfile
import re
import platform
import logging
from pathlib import Path
from typing import Optional, List, Tuple
from dataclasses import dataclass


@dataclass
class BuildConfig:
    """Configuration for the build process."""
    app_name: str = "DeepSeekChat"
    main_file: str = "main.py"
    icon_file: str = "deepseek.ico"
    temp_dir: str = "temp_build"
    dist_dir: str = "built"
    zip_dir: str = "zipped"
    zip_name: str = "build.zip"
    workflow_path: str = ".github/workflows/release.yml"
    required_dirs: List[str] = None
    optional_files: List[str] = None

    def __post_init__(self):
        if self.required_dirs is None:
            self.required_dirs = ["injection"]
        if self.optional_files is None:
            self.optional_files = ["auto-update.bat"]


def setup_logging() -> logging.Logger:
    """Set up logging for the build process."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('build.log', mode='w')
        ]
    )
    return logging.getLogger(__name__)


def get_version_from_workflow(config: BuildConfig, logger: logging.Logger) -> str:
    """Extract version from GitHub workflow file.

    Args:
        config: Build configuration
        logger: Logger instance

    Returns:
        Version string or "0.0.0" if not found
    """
    workflow_path = Path(config.workflow_path)

    if not workflow_path.exists():
        logger.warning(f"Workflow file {workflow_path} not found, using default version")
        return "0.0.0"

    try:
        content = workflow_path.read_text(encoding='utf-8')

        # Look for VERSION: "x.x.x" pattern
        version_match = re.search(r'VERSION:\s*["\']([^"\']+)["\']', content)
        if version_match:
            version = version_match.group(1)
            logger.info(f"Found version in workflow: {version}")
            return version
        else:
            logger.warning("VERSION not found in workflow file, using default")
            return "0.0.0"

    except Exception as e:
        logger.error(f"Error reading workflow file: {e}")
        return "0.0.0"


def validate_requirements(config: BuildConfig, logger: logging.Logger) -> bool:
    """Validate that all required files and directories exist.

    Args:
        config: Build configuration
        logger: Logger instance

    Returns:
        True if all requirements are met, False otherwise
    """
    # Check required directories
    for dir_name in config.required_dirs:
        if not Path(dir_name).exists():
            logger.error(f"Required directory '{dir_name}' not found!")
            return False

    # Check required files
    if not Path(config.icon_file).exists():
        logger.error(f"Required icon file '{config.icon_file}' not found!")
        return False

    if not Path(config.main_file).exists():
        logger.error(f"Required main file '{config.main_file}' not found!")
        return False

    # Check optional files (warn but don't fail)
    for file_name in config.optional_files:
        if not Path(file_name).exists():
            logger.warning(f"Optional file '{file_name}' not found, skipping...")

    return True


def clean_build_directories(config: BuildConfig, logger: logging.Logger) -> None:
    """Clean up previous build artifacts.

    Args:
        config: Build configuration
        logger: Logger instance
    """
    directories_to_clean = [
        config.temp_dir,
        config.dist_dir,
        "build"  # PyInstaller intermediate directory
    ]

    for dir_name in directories_to_clean:
        dir_path = Path(dir_name)
        if dir_path.exists():
            logger.info(f"Removing previous build directory: {dir_name}")
            shutil.rmtree(dir_path, ignore_errors=True)


def create_build_directories(config: BuildConfig, logger: logging.Logger) -> None:
    """Create fresh build directories.

    Args:
        config: Build configuration
        logger: Logger instance
    """
    directories_to_create = [config.temp_dir, config.dist_dir, config.zip_dir]

    for dir_name in directories_to_create:
        dir_path = Path(dir_name)
        dir_path.mkdir(exist_ok=True)
        logger.info(f"Created directory: {dir_name}")


def run_pyinstaller(config: BuildConfig, logger: logging.Logger) -> bool:
    """Run PyInstaller to build the executable.

    Args:
        config: Build configuration
        logger: Logger instance

    Returns:
        True if build succeeded, False otherwise
    """
    icon_path = Path(config.icon_file).resolve()

    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",  # Create Windows GUI app (no console)
        "--noconfirm",  # Disable confirmation dialogs
        f"--icon={icon_path}",
        f"--distpath={config.dist_dir}",
        f"--workpath={config.temp_dir}",
        f"--specpath={config.temp_dir}",
        "-n", config.app_name,
        config.main_file
    ]

    logger.info("Running PyInstaller...")
    logger.debug(f"Command: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)

        # Log output
        if result.stdout:
            logger.info("PyInstaller output:")
            for line in result.stdout.splitlines():
                logger.info(f"  {line}")

        if result.stderr:
            logger.warning("PyInstaller warnings/errors:")
            for line in result.stderr.splitlines():
                logger.warning(f"  {line}")

        if result.returncode != 0:
            logger.error(f"PyInstaller failed with return code {result.returncode}")
            return False

        # Verify the executable was created
        exe_path = Path(config.dist_dir) / f"{config.app_name}.exe"
        if not exe_path.exists():
            logger.error(f"Expected executable {exe_path} was not created")
            return False

        logger.info("PyInstaller completed successfully")
        return True

    except subprocess.TimeoutExpired:
        logger.error("PyInstaller timed out after 10 minutes")
        return False
    except Exception as e:
        logger.error(f"Error running PyInstaller: {e}")
        return False


def build_app() -> int:
    """Main build function.

    Returns:
        0 on success, 1 on failure
    """
    # Set up logging
    logger = setup_logging()
    config = BuildConfig()

    logger.info("Starting DeepSeek Desktop build process...")

    try:
        # Validate requirements
        if not validate_requirements(config, logger):
            logger.error("Build requirements not met")
            return 1

        # Clean and create directories
        clean_build_directories(config, logger)
        create_build_directories(config, logger)

        # Run PyInstaller
        if not run_pyinstaller(config, logger):
            logger.error("PyInstaller build failed")
            return 1

        # Clean up temporary build artifacts
        temp_path = Path(config.temp_dir)
        if temp_path.exists():
            shutil.rmtree(temp_path, ignore_errors=True)
            logger.info("Cleaned up temporary build artifacts")

        # Create version.txt file for auto-updater
        version = get_version_from_workflow(config, logger)
        version_file_path = Path(config.dist_dir) / "version.txt"
        version_file_path.write_text(version)
        logger.info(f"Created version.txt with version: {version}")

        # Copy resources
        if not copy_resources(config, logger):
            logger.error("Failed to copy resources")
            return 1

        # Create ZIP archive
        if not create_zip_archive(config, logger):
            logger.error("Failed to create ZIP archive")
            return 1

        # Open output directory
        open_output_directory(config, logger)

        logger.info("Build completed successfully!")
        return 0

    except Exception as e:
        logger.error(f"Unexpected error during build: {e}")
        return 1


def copy_resources(config: BuildConfig, logger: logging.Logger) -> bool:
    """Copy required resources to the build directory.

    Args:
        config: Build configuration
        logger: Logger instance

    Returns:
        True if all resources copied successfully, False otherwise
    """
    # Define resources to copy to built directory
    resources_to_copy = [
        ("injection", "injection"),  # (source, destination)
        (config.icon_file, config.icon_file),
    ]

    # Add optional files that exist
    for optional_file in config.optional_files:
        if Path(optional_file).exists():
            resources_to_copy.append((optional_file, optional_file))

    # Copy resources
    for src, dest in resources_to_copy:
        src_path = Path(src).resolve()
        dest_path = Path(config.dist_dir) / dest

        try:
            if src_path.is_dir():
                if dest_path.exists():
                    shutil.rmtree(dest_path)
                shutil.copytree(src_path, dest_path)
                logger.info(f"Copied directory: {src} -> {dest}")
            else:
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src_path, dest_path)
                logger.info(f"Copied file: {src} -> {dest}")
        except Exception as e:
            logger.error(f"Failed to copy {src} to {dest}: {e}")
            return False

    return True


def create_zip_archive(config: BuildConfig, logger: logging.Logger) -> bool:
    """Create a ZIP archive of the built application.

    Args:
        config: Build configuration
        logger: Logger instance

    Returns:
        True if ZIP creation succeeded, False otherwise
    """
    zip_path = Path(config.zip_dir) / config.zip_name
    dist_path = Path(config.dist_dir)

    try:
        logger.info(f"Creating ZIP archive: {zip_path}")

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in dist_path.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(dist_path)
                    zipf.write(file_path, arcname)
                    logger.debug(f"Added to ZIP: {arcname}")

        # Verify ZIP was created and has content
        if not zip_path.exists():
            logger.error("ZIP file was not created")
            return False

        zip_size = zip_path.stat().st_size
        if zip_size == 0:
            logger.error("ZIP file is empty")
            return False

        logger.info(f"ZIP archive created successfully: {zip_path} ({zip_size:,} bytes)")
        return True

    except Exception as e:
        logger.error(f"Failed to create ZIP archive: {e}")
        return False


def open_output_directory(config: BuildConfig, logger: logging.Logger) -> None:
    """Open the output directory in the system file manager.

    Args:
        config: Build configuration
        logger: Logger instance
    """
    dist_path = Path(config.dist_dir).resolve()

    try:
        system = platform.system().lower()

        if system == "windows":
            os.startfile(dist_path)
        elif system == "darwin":  # macOS
            subprocess.run(["open", str(dist_path)], check=True)
        elif system == "linux":
            subprocess.run(["xdg-open", str(dist_path)], check=True)
        else:
            logger.warning(f"Unknown operating system: {system}. Cannot open directory automatically.")
            return

        logger.info(f"Opened output directory: {dist_path}")

    except Exception as e:
        logger.warning(f"Failed to open output directory: {e}")
        logger.info(f"Build output is available at: {dist_path}")


def main() -> None:
    """Main entry point for the build script."""
    try:
        exit_code = build_app()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nBuild interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()