# Development Dependencies for DeepSeek Desktop
# Install with: pip install -r requirements-dev.txt

# Include production dependencies
-r requirements.txt

# Testing
pytest>=7.4.0,<8.0.0
pytest-cov>=4.1.0,<5.0.0
pytest-mock>=3.11.0,<4.0.0

# Code quality
black>=23.0.0,<24.0.0
flake8>=6.0.0,<7.0.0
isort>=5.12.0,<6.0.0
mypy>=1.5.0,<2.0.0

# Documentation
sphinx>=7.1.0,<8.0.0
sphinx-rtd-theme>=1.3.0,<2.0.0

# Security scanning
bandit>=1.7.5,<2.0.0
safety>=2.3.0,<3.0.0

# Build tools
build>=0.10.0,<1.0.0
twine>=4.0.0,<5.0.0
