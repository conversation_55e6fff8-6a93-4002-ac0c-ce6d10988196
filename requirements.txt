# DeepSeek Desktop Dependencies
# Pin versions for reproducible builds and security

# Core dependencies
pywebview>=5.0.0,<6.0.0  # Web view component
pyinstaller>=6.0.0,<7.0.0  # Application packaging

# Optional dependencies for enhanced functionality
# Uncomment if needed for specific features:
# requests>=2.31.0,<3.0.0  # HTTP requests (for future API features)
# cryptography>=41.0.0,<42.0.0  # Encryption support (for secure storage)

# Development dependencies (install with: pip install -r requirements-dev.txt)
# pytest>=7.4.0,<8.0.0  # Testing framework
# black>=23.0.0,<24.0.0  # Code formatting
# flake8>=6.0.0,<7.0.0  # Linting