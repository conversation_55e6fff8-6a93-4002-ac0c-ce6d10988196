// DeepSeek Desktop Enhancement Script
// Version: 2.0
// Enhanced with better performance, error handling, and maintainability

(function() {
    'use strict';

    console.log("DeepSeek Desktop Enhancement Script v2.0 loaded");

    // Configuration object for easy maintenance
    const CONFIG = {
        fonts: {
            primary: 'Inter',
            fallback: 'sans-serif',
            googleFontsUrl: 'https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap'
        },
        selectors: {
            // More robust selectors that are less likely to break
            textReplacement: [
                '._0fcaa63._7941d9f',
                '._0fcaa63',
                '[class*="footer"]',
                '[class*="attribution"]'
            ],
            greeting: [
                '._6c7e7df',
                '[class*="greeting"]',
                '[class*="welcome"]'
            ],
            elementsToRemove: [
                '._41b9122',
                '.a1e75851',
                '[class*="ads"]',
                '[class*="promotion"]'
            ]
        },
        animation: {
            typingSpeed: 100,
            deletingSpeed: 50,
            pauseBetween: 2000,
            maxRetries: 5
        },
        performance: {
            observerThrottle: 100,
            maxObservers: 3
        }
    };

    // Utility functions
    const utils = {
        // Throttle function to improve performance
        throttle: function(func, delay) {
            let timeoutId;
            let lastExecTime = 0;
            return function(...args) {
                const currentTime = Date.now();
                if (currentTime - lastExecTime > delay) {
                    func.apply(this, args);
                    lastExecTime = currentTime;
                } else {
                    clearTimeout(timeoutId);
                    timeoutId = setTimeout(() => {
                        func.apply(this, args);
                        lastExecTime = Date.now();
                    }, delay - (currentTime - lastExecTime));
                }
            };
        },

        // Safe element selection with fallback
        selectElements: function(selectors) {
            const elements = [];
            selectors.forEach(selector => {
                try {
                    elements.push(...document.querySelectorAll(selector));
                } catch (e) {
                    console.warn(`Invalid selector: ${selector}`, e);
                }
            });
            return elements;
        },

        // Safe style injection
        injectStyles: function(css) {
            try {
                const style = document.createElement('style');
                style.textContent = css;
                style.setAttribute('data-source', 'deepseek-desktop');
                document.head.appendChild(style);
                return true;
            } catch (e) {
                console.error('Failed to inject styles:', e);
                return false;
            }
        },

        // Safe font loading
        loadFont: function(url) {
            try {
                const link = document.createElement('link');
                link.href = url;
                link.rel = 'stylesheet';
                link.setAttribute('data-source', 'deepseek-desktop');
                document.head.appendChild(link);
                return true;
            } catch (e) {
                console.error('Failed to load font:', e);
                return false;
            }
        }
    };

    // Load Inter font from Google Fonts
    utils.loadFont(CONFIG.fonts.googleFontsUrl);

    // Inject enhanced styles
    const enhancedStyles = `
        /* Enhanced font styling with fallbacks */
        * {
            font-family: '${CONFIG.fonts.primary}', ${CONFIG.fonts.fallback} !important;
        }

        /* Improved sphere cursor styling */
        .typing-cursor {
            display: inline-block;
            width: 0.9em;
            height: 0.9em;
            background-color: currentColor;
            border-radius: 50%;
            margin-left: 0.1em;
            vertical-align: middle;
            animation: blink 1s infinite ease-in-out;
            transition: opacity 0.3s ease;
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        /* Enhanced container for dynamic text */
        .dynamic-container {
            display: inline-block;
            position: relative;
            min-height: 1.2em;
        }

        /* Performance optimization for animations */
        .typing-cursor,
        .dynamic-container {
            will-change: opacity, transform;
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            .typing-cursor {
                animation: none;
                opacity: 1;
            }
        }
    `;

    utils.injectStyles(enhancedStyles);

    // Enhanced text replacement module
    const TextReplacementModule = {
        initialized: new WeakSet(),
        observer: null,

        init: function() {
            this.setupObserver();
            this.processExistingElements();
        },

        processElement: function(element) {
            if (!element || this.initialized.has(element)) return;

            try {
                element.innerHTML = "Made by <a href='https://github.com/LousyBook94' target='_blank' style='opacity: 0.7; text-decoration: none; color: inherit;'>LousyBook01</a>. Powered by <a href='https://deepseek.com/' target='_blank' style='opacity: 0.7; text-decoration: none; color: inherit;'>DeepSeek</a>. Icons by <a href='https://icons8.com/' target='_blank' style='opacity: 0.7; text-decoration: none; color: inherit;'>Icons8</a>";
                this.initialized.add(element);
            } catch (e) {
                console.warn('Failed to process text replacement element:', e);
            }
        },

        processExistingElements: function() {
            const elements = utils.selectElements(CONFIG.selectors.textReplacement);
            elements.forEach(element => this.processElement(element));
        },

        setupObserver: function() {
            if (this.observer) return;

            const throttledCallback = utils.throttle((mutations) => {
                for (const mutation of mutations) {
                    if (mutation.type === 'childList') {
                        const elements = utils.selectElements(CONFIG.selectors.textReplacement);
                        elements.forEach(element => this.processElement(element));
                    }
                }
            }, CONFIG.performance.observerThrottle);

            this.observer = new MutationObserver(throttledCallback);
            this.observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        },

        destroy: function() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
        }
    };

    // Enhanced greeting animation module
    const GreetingModule = {
        initialized: new WeakSet(),
        observer: null,
        activeAnimations: new Map(),

        phrases: [
            "What can I do for you?",
            "Look who's here!",
            "Ready to create something amazing?",
            "Glad to see you!",
            "Let's make some magic!",
            "How may I assist you?",
            "Hope you're having a great day!",
            "What's on your mind?",
            "Ready for new adventures?",
            "Always a pleasure!",
            "Let's dive in!",
            "Your wish is my command!",
            "Feeling creative today?",
            "Let's get to work!",
            "Welcome back!",
            "What's cooking?",
            "Ready for anything!",
            "What's the plan?",
            "Ready to assist!",
            "Let's make it happen!",
            "Always here to help!",
            "Ready to roll!",
            "What's next on our list?",
            "Hope you're doing well!",
            "Let's build something awesome!"
        ],

        getGreeting: function() {
            const now = new Date();
            const hour = now.getHours();

            if (hour >= 5 && hour < 12) {
                return "Good Morning!";
            } else if (hour >= 12 && hour < 18) {
                return "Good Afternoon!";
            } else {
                return "Good Evening!";
            }
        },

        init: function() {
            this.setupObserver();
            this.processExistingElements();
        },

        processExistingElements: function() {
            const elements = utils.selectElements(CONFIG.selectors.greeting);
            elements.forEach(element => this.processElement(element));
        },

        processElement: function(element) {
            if (!element || this.initialized.has(element)) return;

            try {
                this.initGreetingAnimation(element);
                this.initialized.add(element);
            } catch (e) {
                console.warn('Failed to process greeting element:', e);
            }
        },

        initGreetingAnimation: function(greetingElement) {
            // Create unique IDs to avoid conflicts
            const uniqueId = Date.now() + Math.random().toString(36).substr(2, 9);
            const staticId = `static-greeting-${uniqueId}`;
            const dynamicId = `dynamic-text-${uniqueId}`;

            // Create container for dynamic text
            let currentHtml = greetingElement.innerHTML;
            const initialTextPlaceholder = "Hi, I'm DeepSeek.";
            const dynamicContainer = `
                <span class="dynamic-container">
                    <span id="${staticId}"></span>
                    <span id="${dynamicId}"></span>
                    <span class="typing-cursor"></span>
                </span>
            `;

            let updatedHtml;
            if (currentHtml.includes(initialTextPlaceholder)) {
                updatedHtml = currentHtml.replace(initialTextPlaceholder, dynamicContainer);
            } else {
                updatedHtml = `${currentHtml} ${dynamicContainer}`;
            }

            greetingElement.innerHTML = updatedHtml;
            const staticGreetingElement = document.getElementById(staticId);
            const dynamicTextElement = document.getElementById(dynamicId);

            if (!staticGreetingElement || !dynamicTextElement) {
                console.warn('Failed to create greeting elements');
                return;
            }

            // Set initial greeting
            staticGreetingElement.textContent = this.getGreeting();

            // Animation state
            const animationState = {
                phraseIndex: 0,
                charIndex: 0,
                isDeleting: false,
                isActive: true,
                timeoutId: null
            };

            // Store animation reference for cleanup
            this.activeAnimations.set(greetingElement, animationState);

            const typeAnimation = () => {
                if (!animationState.isActive) return;

                const currentPhrase = this.phrases[animationState.phraseIndex];

                if (!animationState.isDeleting) {
                    // Typing mode
                    if (animationState.charIndex < currentPhrase.length) {
                        dynamicTextElement.textContent = currentPhrase.substring(0, animationState.charIndex + 1);
                        animationState.charIndex++;
                        animationState.timeoutId = setTimeout(typeAnimation, CONFIG.animation.typingSpeed);
                    } else {
                        // Finished typing, pause then start deleting
                        animationState.isDeleting = true;
                        animationState.timeoutId = setTimeout(typeAnimation, CONFIG.animation.pauseBetween);
                    }
                } else {
                    // Deleting mode
                    if (animationState.charIndex > 0) {
                        dynamicTextElement.textContent = currentPhrase.substring(0, animationState.charIndex - 1);
                        animationState.charIndex--;
                        animationState.timeoutId = setTimeout(typeAnimation, CONFIG.animation.deletingSpeed);
                    } else {
                        // Finished deleting, move to next phrase
                        animationState.isDeleting = false;
                        animationState.phraseIndex = (animationState.phraseIndex + 1) % this.phrases.length;
                        animationState.timeoutId = setTimeout(typeAnimation, CONFIG.animation.typingSpeed);
                    }
                }
            };

            // Start the typing animation
            typeAnimation();

            // Check for greeting changes every minute
            const greetingInterval = setInterval(() => {
                if (!animationState.isActive) {
                    clearInterval(greetingInterval);
                    return;
                }

                const newGreeting = this.getGreeting();
                if (staticGreetingElement.textContent !== newGreeting) {
                    // Add fade out effect
                    staticGreetingElement.style.transition = 'opacity 0.5s ease';
                    staticGreetingElement.style.opacity = 0;

                    setTimeout(() => {
                        if (animationState.isActive) {
                            // Update greeting and fade in
                            staticGreetingElement.textContent = newGreeting;
                            staticGreetingElement.style.opacity = 1;
                        }
                    }, 500);
                }
            }, 60000); // Check every minute
        },

        setupObserver: function() {
            if (this.observer) return;

            const throttledCallback = utils.throttle((mutations) => {
                for (const mutation of mutations) {
                    if (mutation.type === 'childList') {
                        const elements = utils.selectElements(CONFIG.selectors.greeting);
                        elements.forEach(element => this.processElement(element));
                    }
                }
            }, CONFIG.performance.observerThrottle);

            this.observer = new MutationObserver(throttledCallback);
            this.observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        },

        destroy: function() {
            // Stop all active animations
            this.activeAnimations.forEach((animationState) => {
                animationState.isActive = false;
                if (animationState.timeoutId) {
                    clearTimeout(animationState.timeoutId);
                }
            });
            this.activeAnimations.clear();

            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
        }
    };

    // Element cleanup module
    const CleanupModule = {
        observer: null,

        init: function() {
            this.removeExistingElements();
            this.setupObserver();
        },

        removeExistingElements: function() {
            const elements = utils.selectElements(CONFIG.selectors.elementsToRemove);
            elements.forEach(element => {
                try {
                    console.log(`Removing element with class: ${element.className}`);
                    element.remove();
                } catch (e) {
                    console.warn('Failed to remove element:', e);
                }
            });
        },

        setupObserver: function() {
            if (this.observer) return;

            const throttledCallback = utils.throttle((mutations) => {
                for (const mutation of mutations) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        this.removeExistingElements();
                    }
                }
            }, CONFIG.performance.observerThrottle);

            this.observer = new MutationObserver(throttledCallback);
            this.observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        },

        destroy: function() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
        }
    };

    // Main application controller
    const DeepSeekEnhancer = {
        modules: [TextReplacementModule, GreetingModule, CleanupModule],
        initialized: false,

        init: function() {
            if (this.initialized) return;

            try {
                console.log('Initializing DeepSeek Desktop enhancements...');

                // Initialize all modules
                this.modules.forEach(module => {
                    try {
                        module.init();
                    } catch (e) {
                        console.error(`Failed to initialize module:`, e);
                    }
                });

                this.initialized = true;
                console.log('DeepSeek Desktop enhancements initialized successfully');

                // Set up cleanup on page unload
                window.addEventListener('beforeunload', () => this.destroy());

            } catch (e) {
                console.error('Failed to initialize DeepSeek Desktop enhancements:', e);
            }
        },

        destroy: function() {
            console.log('Cleaning up DeepSeek Desktop enhancements...');

            this.modules.forEach(module => {
                try {
                    if (module.destroy) {
                        module.destroy();
                    }
                } catch (e) {
                    console.error('Error during module cleanup:', e);
                }
            });

            this.initialized = false;
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => DeepSeekEnhancer.init());
    } else {
        DeepSeekEnhancer.init();
    }

    // Expose for debugging (only in development)
    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
        window.DeepSeekEnhancer = DeepSeekEnhancer;
    }

})();